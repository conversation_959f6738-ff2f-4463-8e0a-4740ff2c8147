# Playwright Automation Flow for MinIO Console Login
name: MinIO Console Login Template
description: A reusable flow for attempting to log into a MinIO Console instance at localhost:9001/login.

# Define the variables that the brute-force executor will use.
variables:
  - username
  - password

# Define the failure condition for the brute-force attack.
# The loop will continue if these conditions are NOT met.
failure_condition:
  any:
    # Failure condition: The specific error message becomes visible.
    - type: element_is_visible
      target:
        type: text
        value: "Expected element type <AssumeRoleResponse> but have <ErrorResponse>"

steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://localhost:9001/login"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the MinIO Console login page."

  - name: "Fill in Username"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Username"
    parameters:
      text: "{username}"
    description: "Locates the username input field and types the value from the dictionary."

  - name: "Fill in Password"
    action: "type"
    target:
      type: "role"
      value: "textbox"
      options:
        name: "Password"
    parameters:
      text: "{password}"
    description: "Locates the password input field and types the value from the dictionary."

  - name: "Click Login Button"
    action: "click"
    target:
      type: "role"
      value: "button"
      options:
        name: "Login"
    description: "Locates and clicks the login button to submit the credentials."
